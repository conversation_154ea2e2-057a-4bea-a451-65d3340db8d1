import os
import json
from groq import Groq # Make sure to pip install groq

# --- HARDCODED PATHS ---
# !!! IMPORTANT: MODIFY THESE PATHS TO MATCH YOUR SYSTEM !!!
INPUT_MP3_PATH = "audio_input/2025_05_21_14_46_17.mp3" # e.g., "audio_input/2025_05_21_14_46_17.mp3"
OUTPUT_TXT_PATH = "text_output/transcription_groq2.txt"

# --- GROQ API CONFIGURATION ---
# For Spanish, 'whisper-large-v3-turbo' is good for speed/cost.
# 'whisper-large-v3' offers potentially higher accuracy but costs more.
GROQ_MODEL_ID = "whisper-large-v3"
# GROQ_MODEL_ID = "whisper-large-v3" # Alternative for higher accuracy

def main():
    print("Starting MP3 to text conversion using Groq API...")

    # 1. Check if input file exists
    if not os.path.exists(INPUT_MP3_PATH):
        print(f"ERROR: Input MP3 file not found at: {INPUT_MP3_PATH}")
        print("Please make sure the file exists and the path is correct.")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Looking for: {os.path.abspath(INPUT_MP3_PATH)}")
        return

    # 2. Ensure output directory exists
    output_dir = os.path.dirname(OUTPUT_TXT_PATH)
    if output_dir and not os.path.exists(output_dir):
        print(f"Creating output directory: {output_dir}")
        os.makedirs(output_dir)

    # 3. Initialize Groq Client
    try:
        client = Groq(
    api_key="********************************************************"
)
    except Exception as e:
        print(f"Error initializing Groq client: {e}")
        print("Ensure your GROQ_API_KEY is set correctly.")
        return

    print(f"Groq client initialized. Using model: '{GROQ_MODEL_ID}'")

    # 4. Transcribe the audio
    print(f"Transcribing '{INPUT_MP3_PATH}' with Groq...")
    print("This may take a moment. The API will process the entire file before returning results.")

    try:
        with open(INPUT_MP3_PATH, "rb") as audio_file:
            # For logging "chunks", we request verbose_json and timestamp granularities
            transcription_payload = client.audio.transcriptions.create(
                file=(os.path.basename(INPUT_MP3_PATH), audio_file.read()), # Pass file content
                model=GROQ_MODEL_ID,
                language="es",  # Specify Spanish
                response_format="verbose_json",
                timestamp_granularities=["segment"] # Request segment-level timestamps
                # timestamp_granularities=["word", "segment"] # For word and segment level
            )
        
        # The 'transcription_payload' is a Groq object, often a Pydantic model.
        # To get a dictionary-like structure for easy access, you might need to convert it.
        # Or access its attributes directly if the SDK provides them.
        # Based on typical OpenAI-compatible APIs, it should have attributes.
        
        # Groq SDK returns a model instance. We can access attributes like .text and .segments
        transcribed_text = transcription_payload.text
        segments = transcription_payload.segments if hasattr(transcription_payload, 'segments') else []

        print("\n--- Transcription Segments (from Groq API response) ---")
        if segments:
            for i, segment in enumerate(segments):
                start_time = segment.get('start', 0.0) # Use .get for safety
                end_time = segment.get('end', 0.0)
                segment_text = segment.get('text', '')
                print(f"Segment {i+1} [{start_time:.2f}s -> {end_time:.2f}s]: {segment_text.strip()}")
        elif transcribed_text: # Fallback if segments are not directly available but text is
             print("Segments not explicitly available in this response format, showing full text.")
             print(transcribed_text)
        else:
            print("No transcription text or segments found in the response.")
            # Pretty print the full response for debugging if needed
            # print("\nFull API Response (for debugging):")
            # try:
            #     # If transcription_payload is a Pydantic model, .model_dump_json() is common
            #     print(json.dumps(json.loads(transcription_payload.model_dump_json()), indent=2))
            # except AttributeError:
            #     # Fallback if it's already dict-like or has a different serialization method
            #     print(json.dumps(transcription_payload, indent=2, default=str)) # default=str for non-serializable
            # except Exception as e_json:
            #     print(f"Could not serialize response for debugging: {e_json}")


        print(f"\n--- End of segments ---")
        print(f"Full transcription processing complete.")

    except Exception as e:
        print(f"ERROR during Groq API call or processing: {e}")
        return

    # 5. Save the transcription to the output file
    if transcribed_text:
        try:
            with open(OUTPUT_TXT_PATH, "w", encoding="utf-8") as f:
                f.write(transcribed_text)
            print(f"Full transcription saved to: {OUTPUT_TXT_PATH}")
        except IOError as e:
            print(f"ERROR: Could not write to output file {OUTPUT_TXT_PATH}: {e}")
    else:
        print("No transcribed text received to save.")

    print("Process finished.")

if __name__ == "__main__":
    # Reminder to update the input path
    if INPUT_MP3_PATH == "audio_input/your_audio.mp3": # Default placeholder
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print("!!! PLEASE UPDATE 'INPUT_MP3_PATH' in main.py to your actual MP3 file !!!")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    elif not os.path.exists(INPUT_MP3_PATH):
        print(f"WARNING: Input MP3 file specified does not seem to exist: {INPUT_MP3_PATH}")
        print(f"Please verify the path. Current working directory: {os.getcwd()}")

    main()