from pyannote.audio import Pipeline
import torch
from main_groq import Groq
import json
from datetime import datetime
import os
from pydub import AudioSegment

# Initialize clients
groq_client = Groq(
    api_key="********************************************************"
)

# HuggingFace token
HUGGINGFACE_TOKEN = "*************************************"

# Hardcoded paths
AUDIO_FILE = "ggp3.mp3"
OUTPUT_FILE = "transcription_with_speakers.txt"
LOGS_DIR = "transcription_logs"
SEGMENTS_DIR = "audio_segments"

def setup_directories():
    """Create necessary directories if they don't exist"""
    for directory in [LOGS_DIR, SEGMENTS_DIR]:
        if not os.path.exists(directory):
            os.makedirs(directory)

def log_progress(message):
    """Log progress with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_file = os.path.join(LOGS_DIR, "transcription_progress.log")
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")
    print(message)

def extract_segment(audio, start_ms, end_ms, output_path):
    """Extract a segment from the audio file"""
    segment = audio[start_ms:end_ms]
    segment.export(output_path, format="mp3")
    return output_path

def transcribe_segment(segment_path):
    """Transcribe an audio segment"""
    try:
        with open(segment_path, "rb") as f:
            transcription = groq_client.audio.transcriptions.create(
                file=f,
                model="whisper-large-v3-turbo",
                response_format="text",
                language="en",
                temperature=0
            )
            return transcription if transcription else ""
    except Exception as e:
        log_progress(f"Error transcribing segment: {str(e)}")
        return ""

def process_audio():
    """Process audio file with speaker diarization and transcription"""
    try:
        log_progress("Starting audio processing...")
        
        # Load the full audio file
        log_progress("Loading audio file...")
        audio = AudioSegment.from_mp3(AUDIO_FILE)
        
        # Initialize the pipeline
        pipeline = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1",
            use_auth_token=HUGGINGFACE_TOKEN
        )
        
        # Send pipeline to GPU if available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        pipeline.to(device)
        
        log_progress("Running speaker diarization...")
        diarization = pipeline(AUDIO_FILE)
        
        # Process each speaker turn
        results = []
        for turn, _, speaker in diarization.itertracks(yield_label=True):
            start_ms = int(turn.start * 1000)
            end_ms = int(turn.end * 1000)
            log_progress(f"Processing segment: {turn.start:.1f}s to {turn.end:.1f}s for {speaker}")
            
            # Extract and save segment
            segment_path = os.path.join(SEGMENTS_DIR, f"segment_{len(results):04d}.mp3")
            extract_segment(audio, start_ms, end_ms, segment_path)
            
            # Get transcription for this segment
            transcription = transcribe_segment(segment_path)
            
            # Store result
            result = {
                "start": f"{turn.start:.1f}",
                "end": f"{turn.end:.1f}",
                "speaker": speaker,
                "text": transcription.strip()  # Remove any extra whitespace
            }
            results.append(result)
            
            # Write intermediate results
            with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
                for r in results:
                    # Only write if there's actual text
                    if r["text"]:
                        f.write(f"[{r['start']}s - {r['end']}s] {r['speaker']}: {r['text']}\n")
                    else:
                        f.write(f"[{r['start']}s - {r['end']}s] {r['speaker']}: <no speech detected>\n")
            
            log_progress(f"Saved progress: {len(results)} segments processed")
        
        log_progress(f"Processing completed! Results saved to {OUTPUT_FILE}")
        
    except Exception as e:
        log_progress(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    setup_directories()
    process_audio()


# ---

