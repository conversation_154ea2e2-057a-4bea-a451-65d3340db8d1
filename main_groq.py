import os
import json
from groq import Groq # Make sure to pip install groq

# --- DIRECTORY PATHS ---
MAURICIO_DIR = "mauricio"
TRANSCRIPT_DIR = os.path.join(MAURICIO_DIR, "transcript")

# --- GROQ API CONFIGURATION ---
# For Spanish, 'whisper-large-v3-turbo' is good for speed/cost.
# 'whisper-large-v3' offers potentially higher accuracy but costs more.
GROQ_MODEL_ID = "whisper-large-v3"
# GROQ_MODEL_ID = "whisper-large-v3" # Alternative for higher accuracy

def get_audio_files(directory):
    """Get all audio files from the specified directory."""
    audio_extensions = ['.mp3', '.wav', '.m4a', '.flac', '.ogg', '.aac']
    audio_files = []

    if not os.path.exists(directory):
        print(f"ERROR: Directory not found: {directory}")
        return audio_files

    for file in os.listdir(directory):
        file_path = os.path.join(directory, file)
        if os.path.isfile(file_path):
            _, ext = os.path.splitext(file.lower())
            if ext in audio_extensions:
                audio_files.append(file_path)

    return sorted(audio_files)

def transcribe_audio_file(client, audio_file_path, output_path):
    """Transcribe a single audio file using Groq API."""
    print(f"Transcribing '{audio_file_path}' with Groq...")
    print("This may take a moment. The API will process the entire file before returning results.")

    try:
        with open(audio_file_path, "rb") as audio_file:
            # For logging "chunks", we request verbose_json and timestamp granularities
            transcription_payload = client.audio.transcriptions.create(
                file=(os.path.basename(audio_file_path), audio_file.read()), # Pass file content
                model=GROQ_MODEL_ID,
                language="es",  # Specify Spanish
                response_format="verbose_json",
                timestamp_granularities=["segment"] # Request segment-level timestamps
                # timestamp_granularities=["word", "segment"] # For word and segment level
            )

        # The 'transcription_payload' is a Groq object, often a Pydantic model.
        # To get a dictionary-like structure for easy access, you might need to convert it.
        # Or access its attributes directly if the SDK provides them.
        # Based on typical OpenAI-compatible APIs, it should have attributes.

        # Groq SDK returns a model instance. We can access attributes like .text and .segments
        transcribed_text = transcription_payload.text
        segments = transcription_payload.segments if hasattr(transcription_payload, 'segments') else []

        print("\n--- Transcription Segments (from Groq API response) ---")
        if segments:
            for i, segment in enumerate(segments):
                start_time = segment.get('start', 0.0) # Use .get for safety
                end_time = segment.get('end', 0.0)
                segment_text = segment.get('text', '')
                print(f"Segment {i+1} [{start_time:.2f}s -> {end_time:.2f}s]: {segment_text.strip()}")
        elif transcribed_text: # Fallback if segments are not directly available but text is
             print("Segments not explicitly available in this response format, showing full text.")
             print(transcribed_text)
        else:
            print("No transcription text or segments found in the response.")

        print(f"\n--- End of segments ---")
        print(f"Full transcription processing complete.")

        # Save the transcription to the output file
        if transcribed_text:
            try:
                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(transcribed_text)
                print(f"Full transcription saved to: {output_path}")
                return True
            except IOError as e:
                print(f"ERROR: Could not write to output file {output_path}: {e}")
                return False
        else:
            print("No transcribed text received to save.")
            return False

    except Exception as e:
        print(f"ERROR during Groq API call or processing: {e}")
        return False

def main():
    print("Starting MP3 to text conversion using Groq API...")

    # 1. Check if mauricio directory exists
    if not os.path.exists(MAURICIO_DIR):
        print(f"ERROR: Mauricio directory not found at: {MAURICIO_DIR}")
        print("Please make sure the directory exists.")
        return

    # 2. Get all audio files from mauricio directory
    audio_files = get_audio_files(MAURICIO_DIR)
    if not audio_files:
        print(f"No audio files found in {MAURICIO_DIR}")
        print("Supported formats: .mp3, .wav, .m4a, .flac, .ogg, .aac")
        return

    print(f"Found {len(audio_files)} audio file(s) to process:")
    for audio_file in audio_files:
        print(f"  - {os.path.basename(audio_file)}")

    # 3. Ensure transcript output directory exists
    if not os.path.exists(TRANSCRIPT_DIR):
        print(f"Creating transcript directory: {TRANSCRIPT_DIR}")
        os.makedirs(TRANSCRIPT_DIR)

    # 4. Initialize Groq Client
    try:
        # Try to get API key from environment variable first, then fallback to hardcoded
        api_key = '********************************************************'

        # Try different initialization approaches
        try:
            client = Groq(api_key=api_key)
        except TypeError as te:
            # If there's a TypeError, try without any extra parameters
            print(f"Trying alternative client initialization due to: {te}")
            client = Groq()
            client.api_key = api_key

    except Exception as e:
        print(f"Error initializing Groq client: {e}")
        print("Ensure your GROQ_API_KEY is set correctly.")
        print("You can set it as an environment variable: export GROQ_API_KEY='your_key_here'")
        return

    print(f"Groq client initialized. Using model: '{GROQ_MODEL_ID}'")

    # 5. Process each audio file
    successful_transcriptions = 0
    failed_transcriptions = 0

    for audio_file_path in audio_files:
        print(f"\n{'='*60}")
        print(f"Processing: {os.path.basename(audio_file_path)}")
        print(f"{'='*60}")

        # Generate output filename
        base_name = os.path.splitext(os.path.basename(audio_file_path))[0]
        output_file_path = os.path.join(TRANSCRIPT_DIR, f"{base_name}_transcript.txt")

        # Check if transcript already exists
        if os.path.exists(output_file_path):
            print(f"Transcript already exists: {output_file_path}")
            user_input = input("Do you want to overwrite it? (y/n): ").lower().strip()
            if user_input != 'y' and user_input != 'yes':
                print("Skipping this file...")
                continue

        # Transcribe the audio file
        if transcribe_audio_file(client, audio_file_path, output_file_path):
            successful_transcriptions += 1
        else:
            failed_transcriptions += 1

    # 6. Summary
    print(f"\n{'='*60}")
    print("TRANSCRIPTION SUMMARY")
    print(f"{'='*60}")
    print(f"Total files processed: {len(audio_files)}")
    print(f"Successful transcriptions: {successful_transcriptions}")
    print(f"Failed transcriptions: {failed_transcriptions}")
    print(f"Transcripts saved to: {TRANSCRIPT_DIR}")
    print("Process finished.")

if __name__ == "__main__":
    main()