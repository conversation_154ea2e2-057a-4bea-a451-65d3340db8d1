import assemblyai as aai
from pydub import AudioSegment
import json
import time

# Configure API key
aai.settings.api_key = "adee70ba231649ffb146b4dccf53d6b0"

def transcribe_audio_with_speakers(audio_path):
    """
    Transcribe audio using AssemblyAI with speaker diarization
    
    Parameters:
    - audio_path: Path to local file
    """
    try:
        print(f"Starting transcription of: {audio_path}")
        
        # Create transcriber instance with speaker diarization
        config = aai.TranscriptionConfig(
            speaker_labels=True,  # Enable speaker diarization
            speakers_expected=2  # Optionally specify expected number of speakers
        )
        
        transcriber = aai.Transcriber()
        
        # Submit for transcription
        transcript = transcriber.transcribe(audio_path, config)
        
        if transcript.status == aai.TranscriptStatus.error:
            print(f"Transcription failed: {transcript.error}")
            return None
            
        # Save detailed transcript with speaker information
        with open("transcription_with_speakers.txt", "w", encoding='utf-8') as f:
            f.write(f"Transcript for: {audio_path}\n")
            f.write("=" * 80 + "\n\n")
            
            # Process utterances (segments by speaker)
            for utterance in transcript.utterances:
                start_time = utterance.start / 1000  # Convert to seconds
                end_time = utterance.end / 1000
                speaker = utterance.speaker
                text = utterance.text
                
                # Write formatted output
                f.write(f"[{start_time:.2f}s - {end_time:.2f}s] Speaker {speaker}:\n")
                f.write(f"{text}\n\n")
        
        # Save structured data as JSON for further processing if needed
        with open("transcript_data2.json", "w", encoding='utf-8') as f:
            json_data = {
                "audio_file": audio_path,
                "utterances": [
                    {
                        "speaker": u.speaker,
                        "text": u.text,
                        "start": u.start,
                        "end": u.end,
                        "confidence": u.confidence
                    } for u in transcript.utterances
                ]
            }
            json.dump(json_data, f, indent=2)
                
        print("\nTranscription completed successfully!")
        print(f"Saved transcript to: transcription_with_speakers.txt")
        print(f"Saved structured data to: transcript_data2.json")
        
        return transcript.utterances
        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None

if __name__ == "__main__":
    # Process the audio file
    mp3_file = "./ggp4.mp3"
    
    print("Starting audio processing with speaker diarization...")
    utterances = transcribe_audio_with_speakers(mp3_file)
    
    if utterances:
        print("\nSpeaker Analysis Summary:")
        print("=" * 80)
        
        # Collect statistics
        speaker_stats = {}
        for utterance in utterances:
            speaker = utterance.speaker
            if speaker not in speaker_stats:
                speaker_stats[speaker] = {
                    "total_time": 0,
                    "segment_count": 0,
                    "words": 0
                }
            
            duration = (utterance.end - utterance.start) / 1000  # Convert to seconds
            speaker_stats[speaker]["total_time"] += duration
            speaker_stats[speaker]["segment_count"] += 1
            speaker_stats[speaker]["words"] += len(utterance.text.split())
        
        # Display statistics
        for speaker, stats in speaker_stats.items():
            print(f"\nSpeaker {speaker}:")
            print(f"  Total speaking time: {stats['total_time']:.2f} seconds")
            print(f"  Number of segments: {stats['segment_count']}")
            print(f"  Total words: {stats['words']}")